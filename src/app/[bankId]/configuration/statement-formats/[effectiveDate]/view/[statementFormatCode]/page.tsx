'use client'

import { useRouter } from 'next/navigation'
import { useQuery } from '@tanstack/react-query'
import Link from 'next/link'
import { ChevronRightIcon } from '@heroicons/react/24/outline'

import { Button } from '@/components/Button'
import {
  DetailsSection,
  DetailsSectionItem,
  DetailsSectionItemsRow,
  DetailsSectionTitle,
} from '@/components/DetailsSection'
import { VersionTimeline } from '@/app/[bankId]/configuration/_components/VersionTimeline'

import { unwrap } from '@/lib/unions/unwrap'
import { routeTo, useRoute } from '@/app/[bankId]/configuration/routing'
import { apiToFormSchemas } from '@/api/apiToFormSchema'
import { statementFormatPlanQueries } from '../../../queries'
import { formatToMonthYearFromDate } from '@/lib/date'
import { statementFormatPlanLabels } from '../../../_components/UpdateStatementFormatsFormTypes'
import { returnAddressTypeLabel } from '@/strings/enums'

export default function ViewStatementFormat({}) {
  const router = useRouter()
  const route = unwrap(
    useRoute(),
    '/configuration/statement-formats/[effectiveDate]/view/[statementFormatCode]',
  )!
  const effectiveDate = route.params.effectiveDate
  const payload = {
    code: route.params.statementFormatCode,
  }
  const { data, status, error } = useQuery(
    statementFormatPlanQueries('/getStatementFormatPlansByCode', payload),
  )
  if (status === 'pending') return 'Loading...'
  if (!data || error) {
    return <div>404 Not Found</div>
  }

  const { statementFormatPlan: statementFormatPlanSchema } = apiToFormSchemas
  const statementFormatPlans = data
    .map((item) => {
      const parsed = statementFormatPlanSchema.parse(item)
      return {
        ...parsed,
        effectiveDate: formatToMonthYearFromDate(parsed.effectiveDate),
      }
    })
    .sort(
      (a, b) =>
        new Date(b.effectiveDate).getTime() -
        new Date(a.effectiveDate).getTime(),
    )
  const statementFormat =
    statementFormatPlans.find((item) => item.effectiveDate === effectiveDate) ||
    statementFormatPlans[0]

  const linkFormatter = (
    effectiveDate: string,
    statementFormatCode: string,
  ) => {
    return routeTo(
      '/configuration/statement-formats/[effectiveDate]/view/[statementFormatCode]',
      { ...route.params, effectiveDate, statementFormatCode },
    )
  }

  const onEditClick = () => {
    router.push(
      routeTo(
        '/configuration/statement-formats/[effectiveDate]/edit/[statementFormatCode]',
        {
          ...route.params,
          statementFormatCode: statementFormat.code,
          effectiveDate: statementFormat.effectiveDate,
        },
      ),
    )
  }

  return (
    <div className='w-full overflow-hidden overflow-y-scroll px-8 py-10'>
      <div className='flex items-center justify-between'>
        <div className='flex items-center gap-2'>
          <Link
            href={routeTo(
              '/configuration/statement-formats/[effectiveDate]',
              route.params,
            )}
            className='font-bold'
          >
            ...
          </Link>
          <ChevronRightIcon className='h-4 w-4 text-zinc-400' />
          <div className='text-md text-zinc-500'>
            <Link
              href={routeTo(
                '/configuration/statement-formats/[effectiveDate]',
                route.params,
              )}
              className='font-bold'
            >
              Statement formats
            </Link>
          </div>
          <ChevronRightIcon className='h-4 w-4 text-zinc-400' />
          <div className='text-lg'>{statementFormat.description}</div>
          <div className='text-sm text-zinc-500'>{statementFormat.code}</div>
        </div>
        <Button className='btn-primary text-white' onClick={onEditClick}>
          Edit
        </Button>
      </div>
      <div className='mt-8 flex gap-4'>
        <div className='flex grow flex-col'>
          <DetailsSection>
            <DetailsSectionTitle>
              Statement format information
            </DetailsSectionTitle>
            <DetailsSectionItemsRow>
              <DetailsSectionItem
                label={'Effective date *'}
                info={statementFormat.effectiveDate}
              />
            </DetailsSectionItemsRow>
            <DetailsSectionItemsRow>
              <DetailsSectionItem
                label={'Name *'}
                info={statementFormat.description}
              />
              <DetailsSectionItem
                label={'Code *'}
                info={statementFormat.code}
              />
            </DetailsSectionItemsRow>
          </DetailsSection>

          <DetailsSection className='mt-3'>
            <DetailsSectionTitle>
              Header and visual formatting
            </DetailsSectionTitle>
            <DetailsSectionItemsRow>
              <DetailsSectionItem
                label={'Logo *'}
                info={statementFormat.headerLogoFileName}
              />
            </DetailsSectionItemsRow>
            <DetailsSectionItemsRow>
              <DetailsSectionItem
                label={'Section border color *'}
                info={statementFormat.sectionBorderColor}
              />
              <DetailsSectionItem
                label={'Section background color *'}
                info={statementFormat.sectionBackgroundColor}
              />
            </DetailsSectionItemsRow>
            <DetailsSectionItemsRow>
              <DetailsSectionItem
                label={'Section text color *'}
                info={statementFormat.sectionTextColor}
              />
            </DetailsSectionItemsRow>
            <DetailsSectionItemsRow>
              <DetailsSectionItem
                label={'Apply Border Color to Statement Header'}
                info={statementFormat.enableBorderColor ? 'Yes' : 'No'}
              />
            </DetailsSectionItemsRow>
            <DetailsSectionItemsRow>
              <DetailsSectionItem
                label={'Include officer name'}
                info={statementFormat.includeOfficerName ? 'Yes' : 'No'}
              />
            </DetailsSectionItemsRow>
            <DetailsSectionItemsRow>
              <DetailsSectionItem
                label={'Include officer phone'}
                info={statementFormat.includeOfficerPhone ? 'Yes' : 'No'}
              />
            </DetailsSectionItemsRow>
            <DetailsSectionItemsRow>
              <DetailsSectionItem
                label={'Return address'}
                info={
                  statementFormat.returnAddress ?
                    statementFormat.returnAddress
                  : '--'
                }
              />
            </DetailsSectionItemsRow>
            <DetailsSectionItemsRow>
              <DetailsSectionItem
                label={'Return address Type'}
                info={
                  statementFormat.returnAddressType ?
                    returnAddressTypeLabel(statementFormat.returnAddressType)
                  : '--'
                }
              />
            </DetailsSectionItemsRow>
          </DetailsSection>

          <DetailsSection className='mt-3'>
            <DetailsSectionTitle>
              Statement message and image
            </DetailsSectionTitle>
            <DetailsSectionItemsRow>
              <DetailsSectionItem
                label={'Statement message'}
                info={statementFormat.statementMessage ? 'Yes' : 'No'}
              />
              {statementFormat.statementMessage && (
                <DetailsSectionItem
                  label={'Location *'}
                  info={
                    statementFormat.statementMessageLocation ?
                      statementFormat.statementMessageLocation
                    : '--'
                  }
                />
              )}
            </DetailsSectionItemsRow>

            {statementFormat.statementMessageImages.length > 0 ?
              statementFormat.statementMessageImages.map((_, i) => {
                return (
                  <DetailsSectionItemsRow key={`statement-image-row-${i}`}>
                    <DetailsSectionItem
                      label={'Image'}
                      info={statementFormat.statementMessageImages[i]}
                    />
                    <DetailsSectionItem
                      label={'Location *'}
                      info={statementFormat.statementImageLocations[i]}
                    />
                  </DetailsSectionItemsRow>
                )
              })
            : <DetailsSectionItemsRow>
                <DetailsSectionItem label={'Image'} info={'--'} />
              </DetailsSectionItemsRow>
            }
          </DetailsSection>

          <DetailsSection className='mt-3'>
            <DetailsSectionTitle>Summary options</DetailsSectionTitle>
            <DetailsSectionItemsRow>
              <DetailsSectionItem
                label={'Relationship summary'}
                info={statementFormat.relationshipSummary ? 'Yes' : 'No'}
                tooltip='Relationship summary'
              />
            </DetailsSectionItemsRow>
            {statementFormat.relationshipSummary && (
              <DetailsSectionItemsRow>
                <DetailsSectionItem
                  label={'Label *'}
                  info={statementFormat.relationshipSummaryLabel ? 'Yes' : 'No'}
                />
                <DetailsSectionItem
                  label={'Location *'}
                  info={statementFormat.relationshipSummaryLocation ?? '--'}
                />
              </DetailsSectionItemsRow>
            )}
            <DetailsSectionItemsRow>
              <DetailsSectionItem
                label={'Balance summary'}
                info={statementFormat.balanceSummary ? 'Yes' : 'No'}
              />
            </DetailsSectionItemsRow>

            {statementFormat.balanceSummary && (
              <DetailsSectionItemsRow>
                <DetailsSectionItem
                  label={'Label *'}
                  info={
                    statementFormat.balanceSummaryLabel ?
                      statementFormat.balanceSummaryLabel
                    : '--'
                  }
                />
                <DetailsSectionItem
                  label={'Location *'}
                  info={
                    statementFormat.balanceSummaryLocation ?
                      statementFormat.balanceSummaryLocation
                    : '--'
                  }
                />
              </DetailsSectionItemsRow>
            )}

            <DetailsSectionItemsRow>
              <DetailsSectionItem
                label={'Results summary label *'}
                info={statementFormat.resultsSummaryLabel ?? '--'}
              />
              <DetailsSectionItem
                label={'Location *'}
                info={statementFormat.resultsSummaryLocation ?? '--'}
              />
            </DetailsSectionItemsRow>
            <DetailsSectionItemsRow>
              <DetailsSectionItem
                label={'Size *'}
                info={statementFormat.balanceAndResultsLayout ?? '--'}
              />
            </DetailsSectionItemsRow>
            <DetailsSectionItemsRow>
              <DetailsSectionItem
                label={'Earning credit rate *'}
                info={
                  statementFormatPlanLabels[statementFormat.earningsCreditRate]
                }
                tooltip='Earning credit rate'
              />
            </DetailsSectionItemsRow>
            <DetailsSectionItemsRow>
              <DetailsSectionItem
                label={'Historical analysis summary *'}
                info={
                  statementFormatPlanLabels[
                    statementFormat.historicalSummaryType
                  ]
                }
              />
            </DetailsSectionItemsRow>
            {statementFormat.historicalSummaryType !== 'NO' && (
              <DetailsSectionItemsRow>
                <DetailsSectionItem
                  label={'Label *'}
                  info={
                    statementFormat.historicalSummaryLabel ?
                      statementFormat.historicalSummaryLabel
                    : '--'
                  }
                />
                <DetailsSectionItem
                  label={'Location *'}
                  info={
                    statementFormat.historicalSummaryLocation ?
                      statementFormat.historicalSummaryLocation
                    : '--'
                  }
                />
              </DetailsSectionItemsRow>
            )}
            <DetailsSectionItemsRow>
              <DetailsSectionItem
                label={'Daily balance summary *'}
                info={
                  statementFormatPlanLabels[statementFormat.dailyBalanceSummary]
                }
              />
            </DetailsSectionItemsRow>
            {statementFormat.dailyBalanceSummary !== 'NO' && (
              <DetailsSectionItemsRow>
                <DetailsSectionItem
                  label={'Label *'}
                  info={
                    statementFormat.dailyBalanceSummaryLabel ?
                      statementFormat.dailyBalanceSummaryLabel
                    : '--'
                  }
                />
                <DetailsSectionItem
                  label={'Location *'}
                  info={
                    statementFormat.dailyBalanceSummaryLocation ?
                      statementFormat.dailyBalanceSummaryLocation
                    : '--'
                  }
                />
              </DetailsSectionItemsRow>
            )}
          </DetailsSection>

          <DetailsSection className='mt-3'>
            <DetailsSectionTitle>Service information</DetailsSectionTitle>
            <DetailsSectionItemsRow>
              <DetailsSectionItem
                label={'Service detail label *'}
                info={statementFormat.serviceDetailLabel}
              />
              <DetailsSectionItem
                label={'Location *'}
                info={
                  statementFormat.serviceDetailLocation ?
                    statementFormat.serviceDetailLocation
                  : '--'
                }
              />
            </DetailsSectionItemsRow>
            <DetailsSectionItemsRow>
              <DetailsSectionItem
                label={'AFP Service Code'}
                info={statementFormat.balanceSummary ? 'Yes' : 'No'}
              />
            </DetailsSectionItemsRow>
            <DetailsSectionItemsRow>
              <DetailsSectionItem
                label={'Service code'}
                info={statementFormat.serviceCode ? 'Yes' : 'No'}
              />
            </DetailsSectionItemsRow>
            <DetailsSectionItemsRow>
              <DetailsSectionItem
                label={'Sort services *'}
                info={
                  statementFormatPlanLabels[statementFormat.sortServicesType]
                }
              />
            </DetailsSectionItemsRow>
            <DetailsSectionItemsRow>
              <DetailsSectionItem
                label={'Required balance'}
                info={statementFormat.requiredBalance ? 'Yes' : 'No'}
              />
              <DetailsSectionItem
                label={'Required balance multiplier'}
                info={statementFormat.requiredBalanceMultiplier ? 'Yes' : 'No'}
              />
            </DetailsSectionItemsRow>
            <DetailsSectionItemsRow>
              <DetailsSectionItem
                label={'Services category'}
                info={statementFormat.enableServiceCategory ? 'Yes' : 'No'}
              />
            </DetailsSectionItemsRow>

            <DetailsSectionItemsRow>
              <DetailsSectionItem
                label={'Service category sort'}
                info={
                  statementFormat.serviceCategorySort ?
                    statementFormatPlanLabels[
                      statementFormat.serviceCategorySort
                    ]
                  : '--'
                }
              />
              <DetailsSectionItem
                label={'Service category background color *'}
                info={statementFormat.serviceCategoryBackgroundColor ?? '--'}
              />
            </DetailsSectionItemsRow>
            <DetailsSectionItemsRow>
              <DetailsSectionItem
                label={'Bold services category label'}
                info={statementFormat.boldServiceCategoryLabel ? 'Yes' : 'No'}
              />
            </DetailsSectionItemsRow>
            <DetailsSectionItemsRow>
              <DetailsSectionItem
                label={'Services category subtotal'}
                info={statementFormat.serviceCategorySubtotal ? 'Yes' : 'No'}
              />
            </DetailsSectionItemsRow>
            <DetailsSectionItemsRow>
              <DetailsSectionItem
                label={'Bold services category subtotal label'}
                info={
                  statementFormat.boldServiceCategorySubtotalLabel ? 'Yes' : (
                    'No'
                  )
                }
              />
              <DetailsSectionItem
                label={'Service category subtotal background color'}
                info={
                  statementFormat.serviceCategorySubtotalBackgroundColor ?? '--'
                }
              />
            </DetailsSectionItemsRow>
            <DetailsSectionItemsRow>
              <DetailsSectionItem
                label={'Service charges due bar chart'}
                info={statementFormat.serviceChargesDueBarChart ? 'Yes' : 'No'}
              />
            </DetailsSectionItemsRow>
            {statementFormat.serviceChargesDueBarChart && (
              <DetailsSectionItemsRow>
                <DetailsSectionItem
                  label={'Label *'}
                  info={statementFormat.serviceChargesDueBarChartLabel ?? '--'}
                />
                <DetailsSectionItem
                  label={'Location *'}
                  info={
                    statementFormat.serviceChargesDueBarChartLocation ?? '--'
                  }
                />
              </DetailsSectionItemsRow>
            )}
            <DetailsSectionItemsRow>
              <DetailsSectionItem
                label={'Service category pie chart'}
                info={statementFormat.serviceCategoryPieChart ? 'Yes' : 'No'}
              />
            </DetailsSectionItemsRow>
            {statementFormat.serviceCategoryPieChart && (
              <DetailsSectionItemsRow>
                <DetailsSectionItem
                  label={'Label *'}
                  info={statementFormat.serviceCategoryPieChartLabel ?? '--'}
                />
                <DetailsSectionItem
                  label={'Location *'}
                  info={statementFormat.serviceCategoryPieChartLocation ?? '--'}
                />
              </DetailsSectionItemsRow>
            )}
          </DetailsSection>
        </div>
        <div className='flex max-w-72 flex-col rounded-lg border bg-white p-6'>
          <VersionTimeline
            versions={statementFormatPlans} //todo: update timeline
            currentEffectiveDate={route.params.effectiveDate}
            linkFormatter={linkFormatter}
          />
        </div>
      </div>
    </div>
  )
}
