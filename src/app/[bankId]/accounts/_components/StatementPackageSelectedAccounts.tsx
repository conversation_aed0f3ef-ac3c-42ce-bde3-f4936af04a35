import { ExpandedTable } from '@/components/Table/ExpandedTable'
import {
  AccountCode,
  AccountListItem,
  BoundedAccountListItem,
  StatementPackageFormSchema,
} from '../types'
import { useMemo } from 'react'
import { ColumnDef } from '@tanstack/react-table'
import { Form<PERSON>pi, ReactForm<PERSON>pi, useStore } from '@tanstack/react-form'
import { Checkbox } from '@/components/Checkbox'
import { ChevronDownIcon, ChevronRightIcon } from '@heroicons/react/24/outline'
import { Button as HeadlessButton } from '@headlessui/react'
import clsx from 'clsx'
import { accountCodesAreEqual, accountToAccountCode } from '../accountHelpers'

interface StatementPackageSelectedAccountsProps {
  shortName: string
  accountCode: AccountCode
  subAccounts: BoundedAccountListItem[]
  keyAccountCode?: AccountCode
  form?: ReactFormApi<StatementPackageFormSchema> &
    FormApi<StatementPackageFormSchema>
  readonly?: boolean
  showKeyAccountBadge?: boolean
}

type SelectedAccountsTableItem = Pick<
  AccountListItem,
  'applicationId' | 'bankNumber' | 'accountNumber' | 'shortName' | 'children' | 'isKeyAccount'
>

type StatementPackageSelectedAccountsColumnDef =
  ColumnDef<SelectedAccountsTableItem>

export function StatementPackageSelectedAccounts({
  shortName,
  accountCode,
  subAccounts,
  keyAccountCode,
  form,
  readonly = false,
  showKeyAccountBadge = false,
}: StatementPackageSelectedAccountsProps) {
  // Only use form store if form is provided and not in readonly mode
  const formValues = form && !readonly ? useStore(
    form.store,
    (state) => state.values,
  ) : { packageType: 'ALL_ACCOUNTS', selectedAccounts: [] }

  const { packageType, selectedAccounts } = formValues

  const columns = useMemo<StatementPackageSelectedAccountsColumnDef[]>(
    () => {
      const baseColumns: StatementPackageSelectedAccountsColumnDef[] = []

      // Only add checkbox column if not in readonly mode
      if (!readonly) {
        baseColumns.push({
          id: 'selected',
          header: 'Include',
          meta: {
            className: 'min-w-[100px] justify-center',
          },
          cell: ({ row }) => {
            const matchesRowAccountCode = (selectedAccountCode: AccountCode) =>
              accountCodesAreEqual(selectedAccountCode, row.original)
            const isSelected = !!selectedAccounts.find(matchesRowAccountCode)
            return (
              <Checkbox
                checked={
                  packageType === 'ALL_ACCOUNTS' ||
                  (packageType === 'COMPOSITE_ACCOUNTS' &&
                    row.original.applicationId === 'C') ||
                  (packageType === 'DEPOSIT_ACCOUNTS' &&
                    row.original.applicationId === 'D') ||
                  (packageType === 'SELECTED_ACCOUNTS' && isSelected)
                }
                disabled={packageType !== 'SELECTED_ACCOUNTS'}
                onChange={() => {
                  if (!form) return
                  if (!isSelected) {
                    form.setFieldValue(
                      'selectedAccounts',
                      [...selectedAccounts, accountToAccountCode(row.original)],
                    )
                  } else {
                    form.setFieldValue(
                      'selectedAccounts',
                      selectedAccounts.filter(
                        (selectedAccountCode) =>
                          !matchesRowAccountCode(selectedAccountCode),
                      ),
                    )
                  }
                }}
              />
            )
          },
        })
      }

      baseColumns.push(
        {
          id: 'shortName',
          accessorKey: 'shortName',
          header: 'Account',
          meta: {
            className: 'min-w-[300px] basis-full',
          },
          cell: ({ row, getValue }) => (
            <div
              style={{
                paddingLeft: `${
                  row.id === '0' ? 0
                  : row.getCanExpand() ? row.depth * 2
                  : (row.depth + 1) * 2
                }rem`,
              }}
            >
              <div className='flex flex-row gap-2'>
                {row.getCanExpand() && (
                  <HeadlessButton
                    className={'h-6 w-6'}
                    onClick={row.getToggleExpandedHandler()}
                  >
                    {row.getIsExpanded() ?
                      <ChevronDownIcon />
                    : <ChevronRightIcon />}
                  </HeadlessButton>
                )}
                <div
                  className={clsx(
                    'flex flex-row truncate',
                    row.id === '0' && 'font-semibold',
                  )}
                >
                  {getValue<string>()}
                  {(keyAccountCode &&
                    accountCodesAreEqual(row.original, keyAccountCode)) ||
                    (showKeyAccountBadge && row.original.isKeyAccount && (
                      <div className='ml-2 rounded-md bg-indigo-600 px-2 pt-[1px] text-sm font-light text-indigo-100'>
                        Key account
                      </div>
                    ))}
                </div>
              </div>
            </div>
          ),
        },
        {
          id: 'accountNumber',
          accessorFn: (row) => `${row.applicationId} - ${row.accountNumber}`,
          header: 'Account number',
          meta: {
            className: 'min-w-[200px] basis-full',
          },
          cell: ({ row, getValue }) => (
            <div className={clsx(row.id === '0' && 'font-semibold')}>
              {getValue<string>()}
            </div>
          ),
        },
        /* TODO backend lookup for this was deferred to post-handoff
        {
          id: 'includedInExisting',
          header: 'Included in existing statement packages',
          meta: {
            className: 'min-w-[325px]',
          },
          accessorFn: () => false,
          cell: ({ row, getValue }) => (
            <div className={clsx(row.id === '0' && 'font-semibold')}>
              {getValue<boolean>() ? 'Yes' : 'No'}
            </div>
          ),
        },
        */
      )

      return baseColumns
    },
    [packageType, selectedAccounts, keyAccountCode, form, readonly, showKeyAccountBadge],
  )
  const firstRow = useMemo<SelectedAccountsTableItem[]>(
    () => [
      {
        ...accountCode,
        shortName,
        isKeyAccount: false,
      },
    ],
    [accountCode, shortName],
  )

  return (
    <ExpandedTable
      data={firstRow.concat(subAccounts)}
      columns={columns}
      getSubRows={(row) => row.children}
    />
  )
}
