'use client'

import { accountQueries } from '@/app/[bankId]/accounts/queries'
import { Button } from '@/components/Button'
import {
  InfoSection,
  InfoSectionDescription,
  InfoSectionTitle,
} from '@/components/InfoSection'
import { SortedTable } from '@/components/Table/SortedTable'
import { useQuery } from '@tanstack/react-query'
import { routeTo, useRoute } from '../../routing'
import { data } from '@/lib/unions/Union'
import { useAccountFromParams } from '@/app/[bankId]/accounts/_hooks/useAccountFromParams'
import { useMemo } from 'react'
import { HydratedStatementPackage } from '@/api/formToApiSchema'
import { ColumnDef } from '@tanstack/react-table'
import { formatAddress } from '@/app/[bankId]/accounts/accountHelpers'
import { listSelector } from '@/api/selectors'
import { Address, PackageDeliveryLabels } from '@/app/[bankId]/accounts/types'
import { toUIFormat } from '@/lib/date'
import {
  DetailsSectionItem,
  DetailsSectionItemsRow,
} from '@/components/DetailsSection'
import Link from 'next/link'
import { StatementPackageSelectedAccounts } from '@/app/[bankId]/accounts/_components/StatementPackageSelectedAccounts'

export default function Page() {
  const route = useRoute()!
  const routeParams = data(route).params
  const { accountCode, effectiveDate } = useAccountFromParams(routeParams)

  const {
    data: statementPackages,
    isLoading,
    isError,
    error,
  } = useQuery(
    accountQueries('/listStatementPackages', {
      accountNumber: accountCode.accountNumber,
      applicationId: accountCode.applicationId,
      bankNumber: accountCode.bankNumber,
      effectiveDate,
    }),
  )

  if (isLoading) {
    return <>Loading...</>
  }

  if (isError) {
    return <>{error.message}</>
  }

  if (!statementPackages) {
    return <>There was an error</>
  }

  // TODO NEED /getStatementPackage by account code and statement package number
  const statementPackageDetail = statementPackages.find(
    (item) =>
      item.statementPackage.statementPackageNumber.toString() ===
      routeParams.packageNumber,
  )

  if (!statementPackageDetail) {
    return <>There was an error</>
  }
  return (
    <>
    <InfoSection>
      <div className='flex items-center justify-between pb-4'>
        <div>
          <InfoSectionTitle>Statement package information</InfoSectionTitle>
        </div>
        <div className='flex gap-2'>
          <Button>Delete</Button>
          <Link
            href={`${routeTo('/accounts/[effectiveMonth]/view/[accountCode]/statement-package/edit/[packageNumber]', routeParams)}`}
          >
            <Button className='btn-primary text-white'>Edit</Button>
          </Link>
        </div>
      </div>
      <DetailsSectionItemsRow>
        <DetailsSectionItem
          label='Effective date'
          info={statementPackageDetail.statementPackage.effectiveDate}
        />
      </DetailsSectionItemsRow>
      <DetailsSectionItemsRow>
        <DetailsSectionItem
          label='Package code'
          info={statementPackageDetail.statementPackage.statementPackageNumber}
        />
        <DetailsSectionItem
          label='Delivery method'
          info={
            PackageDeliveryLabels[
              statementPackageDetail.statementPackage.packageDelivery
            ]
          }
        />
      </DetailsSectionItemsRow>
      <DetailsSectionItemsRow>
        <DetailsSectionItem
          label='Delivery address'
          info={formatAddress(statementPackageDetail.address as Address)}
        />
      </DetailsSectionItemsRow>
      <DetailsSectionItemsRow>
        <DetailsSectionItem label='Include accounts' info={''} />
      </DetailsSectionItemsRow>
    </InfoSection>
    <div>

      {/* fetch subAccounts from SelectAccounts.tsx

      const accountsMap = mappingsToAccountsMap([
      which fetches data from 
      const mappings = await queryClient.fetchQuery(
                            accountQueries('/getAccountMappings', {
                              code: selectedAccountCode,
                              effectiveDate,
                            }),
                          )
      this mappings is send to mappingsToAccountsMap in SelectAccounts.tsx
      
      */}
      <StatementPackageSelectedAccounts
        accountCode={accountCode}
        shortName={accountInfo.shortName}
        subAccounts={subAccounts}
        readonly={true}
      />
    </div>
    </>
  )
}
